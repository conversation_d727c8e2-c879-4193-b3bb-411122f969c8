import os
import pandas as pd
import matplotlib.pyplot as plt


# Channel center frequencies in THz (from ch1 to ch14)
freq_centers = [194.46418, 194.56364, 194.6631 , 194.76256, 194.86202, 194.96148, 195.06094, 195.1604 ,
                195.25986, 195.35932, 195.45878, 195.55824, 195.6577 , 195.75716]

def load_ber_data(filepath):
    """Load BER data from the log file."""
    try:
        data = pd.read_csv(filepath)
        return data
    except FileNotFoundError:
        print(f"Error: File {filepath} not found.")
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def plot_ber_over_frequency(data, freq_centers):
    """Plot BER vs channel frequency."""
    if data is None:
        return

    # Ensure we have the right number of frequency points
    if len(freq_centers) != len(data):
        print(f"Warning: Number of frequency centers ({len(freq_centers)}) doesn't match data points ({len(data)})")
        # Use only the available data points
        min_len = min(len(freq_centers), len(data))
        freq_centers = freq_centers[:min_len]
        data = data.iloc[:min_len]

    # Create the plot
    plt.figure(figsize=(12, 8))

    # Plot BER vs frequency
    plt.subplot(2, 1, 1)
    plt.semilogy(freq_centers, data['BER'], 'bo-', linewidth=2, markersize=8)
    plt.xlabel('Frequency (THz)')
    plt.ylabel('Bit Error Ratio (BER)')
    plt.title('BER vs Channel Frequency (16QAM, 87km)')
    plt.grid(True, alpha=0.3)

    # Add channel labels
    for i, (freq, ber) in enumerate(zip(freq_centers, data['BER'])):
        plt.annotate(f'CH{i+1}', (freq, ber), textcoords="offset points",
                    xytext=(0,10), ha='center', fontsize=9)

    # Plot SNR vs frequency for reference
    plt.subplot(2, 1, 2)
    plt.plot(freq_centers, data['SNR'], 'ro-', linewidth=2, markersize=8)
    plt.xlabel('Frequency (THz)')
    plt.ylabel('SNR (dB)')
    plt.title('SNR vs Channel Frequency (16QAM, 87km)')
    plt.grid(True, alpha=0.3)

    # Add channel labels
    for i, (freq, snr) in enumerate(zip(freq_centers, data['SNR'])):
        plt.annotate(f'CH{i+1}', (freq, snr), textcoords="offset points",
                    xytext=(0,10), ha='center', fontsize=9)

    plt.tight_layout()
    plt.show()

def print_summary_statistics(data, freq_centers):
    """Print summary statistics of the BER measurements."""
    if data is None:
        return

    print("\n=== BER Measurement Summary ===")
    print(f"Number of channels: {len(data)}")
    print(f"Frequency range: {min(freq_centers):.3f} - {max(freq_centers):.3f} THz")
    print(f"Average BER: {data['BER'].mean():.2e}")
    print(f"Best BER: {data['BER'].min():.2e} (Channel {data.loc[data['BER'].idxmin(), 'CH']})")
    print(f"Worst BER: {data['BER'].max():.2e} (Channel {data.loc[data['BER'].idxmax(), 'CH']})")
    print(f"Average SNR: {data['SNR'].mean():.2f} dB")
    print(f"Best SNR: {data['SNR'].max():.2f} dB (Channel {data.loc[data['SNR'].idxmax(), 'CH']})")
    print(f"Worst SNR: {data['SNR'].min():.2f} dB (Channel {data.loc[data['SNR'].idxmin(), 'CH']})")

if __name__ == "__main__":
    filedir = r'R:\Peng\QDMLLD_WDM\Demodulation results'
    filenames = [r'16QAM_demod_OB2B_log.csv', r'16QAM_demod_87km_log.csv', r'32QAM_demod_OB2B_log.csv']
    ber_data = load_ber_data(os.path.join(filedir, filenames[0]))

    if ber_data is not None:
        # Print summary statistics
        print_summary_statistics(ber_data, freq_centers)

        # Create the plot
        plot_ber_over_frequency(ber_data, freq_centers)
    else:
        print("Failed to load BER data. Please check the file path and format.")
